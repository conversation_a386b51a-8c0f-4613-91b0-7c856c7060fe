package com.challanty.android.implode.ui.screens.gameplay

import javax.inject.Inject
import javax.inject.Singleton

/**
 * Handles all game scoring logic following AGENT.md specifications.
 *
 * Solo Game Scoring Formula:
 * 1. Base Score (Per Cluster Removed): Sbase = 100
 * 2. Size Bonus (Per Cluster Removed): Ssize_bonus = (number of squares in cluster)^2
 * 3. Speed Bonus (Per Cluster Removed): Sspeed_bonus = (1000/milliseconds elapsed) * (Sbase + Ssize_bonus)
 * 4. Cluster Score (Per Cluster Removed): Scluster = Sbase + Ssize_bonus + Sspeed_bonus
 * 5. Current Total Cluster Removal Score: Scurrent_total = ΣScluster
 * 6. All-One-Color-In-Game Cluster Bonuses: Sall_color_bonus_1 through 5 = 1000 each
 * 7. Clearing the Board Bonus: Sclearing_bonus = 1000
 * 8. Penalty for Not Clearing the Board: Sclearing_penalty = 100 * (number of squares remaining)
 * 9. Final Score Solo Game: Sfinal = Scurrent_total + Sall_color_bonus_1 through 5 + Sclearing_bonus - Sclearing_penalty
 *
 * This class is stateless and thread-safe.
 */
@Singleton
class GameScoring @Inject constructor() {
    
    companion object {
        // Constants for new scoring formula
        private const val BASE_SCORE = 100
        private const val SPEED_BONUS_BASE = 1000
        private const val ALL_COLOR_BONUS = 1000
        private const val CLEARING_BONUS = 1000
        private const val CLEARING_PENALTY_PER_SQUARE = 100
    }
    
    /**
     * Represents the current scoring state.
     * This is a lightweight data class for passing scoring information.
     */
    data class ScoringState(
        val currentScore: Int = 0,
        val lastMoveTime: Long = System.currentTimeMillis(),
        val allColorBonusesAwarded: Set<SquareColor> = emptySet(),
        val initialColorCounts: Map<SquareColor, Int> = emptyMap(),
        val currentTotalClusterScore: Int = 0  // Sum of all cluster scores (Scurrent_total)
    ) {
        fun withScore(newScore: Int): ScoringState = copy(currentScore = newScore)
        fun withLastMoveTime(time: Long): ScoringState = copy(lastMoveTime = time)
        fun withAllColorBonusAwarded(color: SquareColor): ScoringState = copy(allColorBonusesAwarded = allColorBonusesAwarded + color)
        fun withInitialColorCounts(counts: Map<SquareColor, Int>): ScoringState = copy(initialColorCounts = counts)
        fun withCurrentTotalClusterScore(total: Int): ScoringState = copy(currentTotalClusterScore = total)
    }
    
    /**
     * Represents the result of a scoring calculation.
     */
    data class ScoreResult(
        val baseScore: Int,
        val sizeBonus: Int,
        val speedBonus: Int,
        val allColorBonus: Int,
        val allColorBonusText: String,
        val totalScore: Int,
        val clusterScore: Int = baseScore + sizeBonus + speedBonus  // Scluster = Sbase + Ssize_bonus + Sspeed_bonus
    ) {
        val hasSpeedBonus: Boolean get() = speedBonus > 0
        val hasAllColorBonus: Boolean get() = allColorBonus > 0
    }
    
    /**
     * Calculates the score for a cluster removal and returns both the result and updated state.
     * This is the main entry point for scoring calculations.
     */
    fun calculateClusterScore(
        clusterSize: Int,
        clusterColor: SquareColor,
        scoringState: ScoringState
    ): Pair<ScoreResult, ScoringState> {
        val currentTime = System.currentTimeMillis()

        // Calculate base score: Sbase = 100
        val baseScore = BASE_SCORE

        // Calculate size bonus: Ssize_bonus = (number of squares in cluster)^2
        val sizeBonus = calculateSizeBonus(clusterSize)

        // Calculate speed bonus: Sspeed_bonus = (1000/milliseconds elapsed) * (Sbase + Ssize_bonus)
        val speedBonus = calculateSpeedBonus(currentTime, scoringState.lastMoveTime, baseScore + sizeBonus)

        // Calculate cluster score: Scluster = Sbase + Ssize_bonus + Sspeed_bonus
        val clusterScore = baseScore + sizeBonus + speedBonus

        // Update current total cluster score (Scurrent_total)
        val newCurrentTotalClusterScore = scoringState.currentTotalClusterScore + clusterScore

        // Check for all-color bonus
        val (allColorBonus, updatedState) = checkAllColorBonus(
            clusterSize,
            clusterColor,
            scoringState.withCurrentTotalClusterScore(newCurrentTotalClusterScore)
        )
        val allColorBonusText = if (allColorBonus > 0) {
            "All ${clusterColor.name} cleared! +$allColorBonus"
        } else ""

        // Total score includes cluster score and any all-color bonus
        val totalScore = clusterScore + allColorBonus

        val scoreResult = ScoreResult(
            baseScore = baseScore,
            sizeBonus = sizeBonus,
            speedBonus = speedBonus,
            allColorBonus = allColorBonus,
            allColorBonusText = allColorBonusText,
            totalScore = totalScore,
            clusterScore = clusterScore
        )

        val finalState = updatedState.withLastMoveTime(currentTime)

        println("GameScoring: base=$baseScore, size=$sizeBonus, speed=$speedBonus, cluster=$clusterScore, allColor=$allColorBonus, total=$totalScore")

        return Pair(scoreResult, finalState)
    }
    
    /**
     * Calculates size bonus: Ssize_bonus = (number of squares in cluster)^2
     */
    private fun calculateSizeBonus(clusterSize: Int): Int {
        return clusterSize * clusterSize
    }

    /**
     * Calculates speed bonus: Sspeed_bonus = (1000/milliseconds elapsed) * (Sbase + Ssize_bonus)
     */
    private fun calculateSpeedBonus(currentTime: Long, lastMoveTime: Long, baseAndSizeScore: Int): Int {
        val millisecondsElapsed = currentTime - lastMoveTime
        if (millisecondsElapsed <= 0) return 0

        val speedBonusMultiplier = SPEED_BONUS_BASE.toDouble() / millisecondsElapsed.toDouble()
        return (speedBonusMultiplier * baseAndSizeScore).toInt()
    }
    
    /**
     * Checks for all-one-color bonus: Sall_color_bonus = 1000
     *
     * The bonus is only awarded when the cluster size being removed equals the number
     * of squares of that color that were present on the starting board, and this color
     * hasn't already been awarded a bonus.
     */
    private fun checkAllColorBonus(
        clusterSize: Int,
        clusterColor: SquareColor,
        scoringState: ScoringState
    ): Pair<Int, ScoringState> {
        val initialCount = scoringState.initialColorCounts[clusterColor] ?: 0

        // Check if the cluster size equals the original count from the starting board
        // and this color hasn't already been awarded a bonus
        if (clusterSize == initialCount && !scoringState.allColorBonusesAwarded.contains(clusterColor)) {
            println("All-color bonus! Cleared $clusterSize $clusterColor squares (matches starting board count of $initialCount). Bonus: $ALL_COLOR_BONUS")

            val updatedState = scoringState.withAllColorBonusAwarded(clusterColor)
            return Pair(ALL_COLOR_BONUS, updatedState)
        }

        return Pair(0, scoringState)
    }

    /**
     * Calculates clearing the board bonus for finishing the game.
     * Formula: Sclearing_bonus = 1000
     */
    fun calculateClearingBonus(): ScoreResult {
        println("GameScoring - Clearing bonus: $CLEARING_BONUS")
        return ScoreResult(
            baseScore = 0,
            sizeBonus = 0,
            speedBonus = 0,
            allColorBonus = 0,
            allColorBonusText = if (CLEARING_BONUS > 0) "Board Cleared! +$CLEARING_BONUS" else "",
            totalScore = CLEARING_BONUS,
            clusterScore = 0
        )
    }

    /**
     * Calculates penalty for not clearing the board when the game ends.
     * Formula: Sclearing_penalty = 100 * (number of squares remaining)
     */
    fun calculateClearingPenalty(remainingSquares: Int): ScoreResult {
        val penalty = CLEARING_PENALTY_PER_SQUARE * remainingSquares
        println("GameScoring - Clearing penalty: $penalty (${remainingSquares} squares remaining)")
        return ScoreResult(
            baseScore = 0,
            sizeBonus = 0,
            speedBonus = 0,
            allColorBonus = 0,
            allColorBonusText = if (penalty > 0) "Squares Remaining: -$penalty" else "",
            totalScore = -penalty,  // Negative because it's a penalty
            clusterScore = 0
        )
    }
}
